<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nakama 网络架构图</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
        }
        .layer-box {
            background-color: rgba(255, 255, 255, 0.7);
            border: 1px solid #e2e8f0;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }
        .component-card {
            background-color: #ffffff;
            border: 1px solid #cbd5e1;
            border-radius: 0.75rem;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }
        .component-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.1);
        }
        .component-title {
            font-weight: 600;
            color: #1e293b;
            margin-top: 0.75rem;
        }
        .component-subtitle {
            font-size: 0.75rem;
            color: #64748b;
            margin-top: 0.25rem;
        }
        .layer-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .icon {
            width: 2.5rem;
            height: 2.5rem;
            stroke-width: 1.5;
        }
        .line-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        .line {
            stroke: #94a3b8;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        .dashed-line {
            stroke-dasharray: 6, 4;
        }
    </style>
</head>
<body class="p-4 sm:p-6 md:p-8">

    <div class="max-w-7xl mx-auto relative">
        <h1 class="text-3xl font-bold text-center text-slate-800 mb-4">Nakama 整体架构</h1>
        <p class="text-center text-slate-600 mb-12 max-w-3xl mx-auto">一个可扩展的、用于游戏和实时应用的开源后端服务器架构可视化展示。</p>

        <!-- SVG for drawing connection lines -->
        <div class="line-container">
            <svg width="100%" height="100%" id="svg-connections">
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#94a3b8" />
                    </marker>
                </defs>
            </svg>
        </div>

        <!-- 客户端层 -->
        <div id="client-layer" class="layer-box">
            <h2 class="layer-title">客户端层 (Client)</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div id="c1" class="component-card">
                    <svg class="icon text-sky-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9V3m-9 9h18" /></svg>
                    <h3 class="component-title">Web 客户端</h3>
                    <p class="component-subtitle">WebSocket + JSON</p>
                </div>
                <div id="c2" class="component-card">
                    <svg class="icon text-violet-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>
                    <h3 class="component-title">PC/移动客户端</h3>
                    <p class="component-subtitle">KCP / rUDP + Protobuf</p>
                </div>
                <div id="c3" class="component-card">
                    <svg class="icon text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5 0-2.268-2.268M6 9l-2.268 2.268m12.481 2.532L15 14.25m-4.5 0-2.268 2.268M9 6l-2.268-2.268" /></svg>
                    <h3 class="component-title">所有客户端</h3>
                    <p class="component-subtitle">gRPC / HTTP REST</p>
                </div>
            </div>
        </div>

        <!-- 接口层 -->
        <div id="interface-layer" class="layer-box">
            <h2 class="layer-title">接口层 (Interface Layer)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div id="ws" class="component-card">
                    <svg class="icon text-cyan-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 21v-1.5M12 3v1.5m0 15V21m3.75-18v1.5M12 8.25v7.5" /></svg>
                    <h3 class="component-title">Socket 网关</h3>
                    <p class="component-subtitle">WebSocket / rUDP</p>
                </div>
                <div id="api" class="component-card">
                     <svg class="icon text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" /></svg>
                    <h3 class="component-title">API 网关</h3>
                    <p class="component-subtitle">gRPC / HTTP REST</p>
                </div>
            </div>
        </div>

        <!-- 核心服务层 -->
        <div id="core-layer" class="layer-box">
            <h2 class="layer-title">核心服务层 (Core Services)</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                <div id="auth" class="component-card"><svg class="icon text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.286zm0 13.036h.008v.008h-.008v-.008z" /></svg><h3 class="component-title">认证 & 会话</h3><p class="component-subtitle">JWT Auth</p></div>
                <div id="pres" class="component-card"><svg class="icon text-lime-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m-7.5-2.962a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM6 20.25a9.094 9.094 0 013.742-.48 3 3 0 014.682-2.72m-7.5-2.962a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0z" /></svg><h3 class="component-title">状态追踪</h3><p class="component-subtitle">Presence Tracker</p></div>
                <div id="match" class="component-card"><svg class="icon text-rose-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 1.5m1-1.5l1 1.5m0 0l-1 1.5m1-1.5l1 1.5M9 11.25l1.5 1.5L12 11.25m-1.5-1.5l1.5-1.5L12 9.75M15 11.25l1.5 1.5L18 11.25m-1.5-1.5l1.5-1.5L18 9.75m-7.5 6.75h7.5" /></svg><h3 class="component-title">匹配管理</h3><p class="component-subtitle">Real-time Engine</p></div>
                <div id="chat" class="component-card"><svg class="icon text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" /></svg><h3 class="component-title">聊天/流管理</h3><p class="component-subtitle">Chat / Stream Manager</p></div>
                <div id="storage" class="component-card"><svg class="icon text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375" /></svg><h3 class="component-title">存储层</h3><p class="component-subtitle">In-memory / DB Sync</p></div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 持久化存储 -->
            <div id="persistence-layer" class="layer-box">
                <h2 class="layer-title">持久化存储 (Persistent Storage)</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div id="db" class="component-card">
                        <svg class="icon text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s8-1.79 8-4" /></svg>
                        <h3 class="component-title">数据库</h3>
                        <p class="component-subtitle">PostgreSQL / CockroachDB</p>
                    </div>
                    <div id="cache" class="component-card">
                        <svg class="icon text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" /></svg>
                        <h3 class="component-title">内存缓存</h3>
                        <p class="component-subtitle">In-Memory Cache</p>
                    </div>
                </div>
            </div>

            <!-- 运维监控 -->
            <div id="devops-layer" class="layer-box">
                <h2 class="layer-title">运维监控 (DevOps & Observability)</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div id="console" class="component-card">
                        <svg class="icon text-fuchsia-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-1.621-1.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25A2.25 2.25 0 015.25 3h4.5a2.25 2.25 0 012.25 2.25" /></svg>
                        <h3 class="component-title">管理后台 UI</h3>
                        <p class="component-subtitle">Port 7351</p>
                    </div>
                    <div id="metrics" class="component-card">
                        <svg class="icon text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z" /></svg>
                        <h3 class="component-title">Prometheus 导出</h3>
                        <p class="component-subtitle">Metrics Exporter</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 集群层 (企业版) -->
        <div id="cluster-layer" class="layer-box border-2 border-dashed border-sky-500">
            <h2 class="layer-title">集群层 (Cluster - 企业版功能)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                 <div class="flex flex-col gap-6">
                    <div id="node1" class="component-card"><svg class="icon text-sky-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3V7.5a3 3 0 013-3h13.5a3 3 0 013 3v3.75a3 3 0 01-3 3m-13.5 0v1.5a3 3 0 003 3h7.5a3 3 0 003-3v-1.5" /></svg><h3 class="component-title">Nakama 节点 A</h3></div>
                    <div id="node2" class="component-card"><svg class="icon text-sky-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3V7.5a3 3 0 013-3h13.5a3 3 0 013 3v3.75a3 3 0 01-3 3m-13.5 0v1.5a3 3 0 003 3h7.5a3 3 0 003-3v-1.5" /></svg><h3 class="component-title">Nakama 节点 B</h3></div>
                 </div>
                 <div class="flex flex-col gap-6">
                     <div id="gossip" class="component-card"><svg class="icon text-sky-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" /></svg><h3 class="component-title">Gossip 协议</h3><p class="component-subtitle">Port 7352</p></div>
                     <div id="rpc" class="component-card"><svg class="icon text-sky-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.82m5.84-2.56a12.025 12.025 0 01-4.132 4.965m0 0a12.025 12.025 0 01-4.132-4.965m4.132 4.965L11.41 16.51m0 0a12.025 12.025 0 01-4.132-4.965m4.132 4.965L9.77 18.75m0 0a12.025 12.025 0 01-4.132-4.965M11.41 16.51l-1.64-1.64m1.64 1.64L9.77 18.75m6.521-3.38a12.025 12.025 0 01-4.132 4.965M18.63 13.13a12.025 12.025 0 01-4.132 4.965" /></svg><h3 class="component-title">集群 RPC</h3><p class="component-subtitle">Port 7353</p></div>
                 </div>
            </div>
        </div>

    </div>

    <script>
        // This script draws lines between the different architecture components.
        // It runs after the page has loaded to ensure all elements are in the correct positions.
        function drawLines() {
            const svg = document.getElementById('svg-connections');
            const container = svg.parentElement;
            
            // Clear existing lines to allow for redraw on resize
            svg.innerHTML = '<defs>' + svg.querySelector('defs').innerHTML + '</defs>';

            // Map of connections: [startElementId, endElementId, options]
            const connections = [
                ['c1', 'ws', {}],
                ['c2', 'ws', {}],
                ['c3', 'api', {}],
                ['ws', 'auth', {}],
                ['ws', 'pres', {}],
                ['ws', 'match', {}],
                ['api', 'auth', {}],
                ['match', 'storage', {}],
                ['storage', 'db', {}],
                ['storage', 'cache', {}],
                ['node1', 'gossip', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['node2', 'gossip', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['gossip', 'node1', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['gossip', 'node2', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['node1', 'rpc', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['node2', 'rpc', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['rpc', 'node1', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['rpc', 'node2', { oneWay: true, dashed: true, color: '#38bdf8' }],
                ['console', 'metrics', { oneWay: true }]
            ];

            connections.forEach(([startId, endId, options]) => {
                const startElem = document.getElementById(startId);
                const endElem = document.getElementById(endId);
                if (startElem && endElem) {
                    drawLine(svg, container, startElem, endElem, options);
                }
            });
        }

        function drawLine(svg, container, startElem, endElem, options) {
            const containerRect = container.getBoundingClientRect();
            const startRect = startElem.getBoundingClientRect();
            const endRect = endElem.getBoundingClientRect();

            const startX = startRect.left + startRect.width / 2 - containerRect.left;
            const startY = startRect.top + startRect.height - containerRect.top;
            const endX = endRect.left + endRect.width / 2 - containerRect.left;
            const endY = endRect.top - containerRect.top;

            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const d = `M ${startX} ${startY} C ${startX} ${startY + 60}, ${endX} ${endY - 60}, ${endX} ${endY}`;
            path.setAttribute('d', d);
            path.setAttribute('class', 'line');
            
            if (options.dashed) {
                path.classList.add('dashed-line');
            }
            if (options.color) {
                path.style.stroke = options.color;
            }

            if(options.oneWay) {
                 path.setAttribute('marker-end', 'url(#arrowhead)');
            } else {
                 path.setAttribute('marker-start', 'url(#arrowhead)');
                 path.setAttribute('marker-end', 'url(#arrowhead)');
            }

            svg.appendChild(path);
        }

        // Draw lines on load and on window resize
        window.addEventListener('load', drawLines);
        window.addEventListener('resize', drawLines);

    </script>
</body>
</html>

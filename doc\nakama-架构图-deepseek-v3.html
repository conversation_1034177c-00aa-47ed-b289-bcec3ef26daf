<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏服务器网络架构图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }
        .architecture-diagram {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
        }
        .tech-specs {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        .tech-specs h3 {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .tech-list {
            columns: 2;
            column-gap: 30px;
        }
        .tech-list li {
            margin-bottom: 8px;
            break-inside: avoid;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>多人在线游戏网络架构图</h1>
        
        <div class="architecture-diagram">
            <div class="mermaid">
                graph TD
                    %% ===== 客户端层 =====
                    subgraph Client["客户端层"]
                        C1["Web客户端\nWebSocket + JSON"]
                        C2["PC/移动端\nKCP/rUDP + Protobuf"]
                        C3["全平台客户端\ngRPC/HTTP REST"]
                    end

                    %% ===== 接口层 =====
                    subgraph Interface_Layer["接入网关层"]
                        WS["Socket网关\nWebSocket/rUDP"]
                        API["API网关\ngRPC/HTTP REST"]
                    end

                    %% ===== 核心服务层 =====
                    subgraph Core_Services["核心服务层"]
                        AUTH["认证服务\nJWT Auth"]
                        PRES["状态追踪"]
                        MATCH["匹配引擎\n实时计算"]
                        CHAT["聊天服务"]
                        STORAGE["存储服务\n内存+DB同步"]
                    end

                    %% ===== 数据层 =====
                    subgraph Persistent_Storage["数据持久层"]
                        DB["数据库集群\nPostgreSQL/CockroachDB"]
                        CACHE["缓存集群\nRedis"]
                    end

                    %% ===== 基础设施层 =====
                    subgraph Cluster["服务器集群"]
                        NODE1["节点A"]
                        NODE2["节点B"]
                        GOSSIP["集群通信\nGossip协议:7352"]
                        RPC["内部RPC\n端口:7353"]
                    end

                    %% ===== 运维监控 =====
                    subgraph DevOps_Observability["监控系统"]
                        CONSOLE["管理控制台\n端口:7351"]
                        METRICS["指标收集\nPrometheus"]
                    end

                    %% ===== 连接关系 =====
                    C1 -->|长连接| WS
                    C2 -->|可靠UDP| WS
                    C3 -->|API调用| API
                    
                    WS -->|鉴权| AUTH
                    WS -->|状态同步| PRES
                    WS -->|匹配请求| MATCH
                    API -->|访问控制| AUTH
                    
                    MATCH -->|数据持久化| STORAGE
                    STORAGE -->|主存储| DB
                    STORAGE -->|缓存| CACHE
                    
                    NODE1 -->|集群通信| GOSSIP
                    NODE2 -->|集群通信| GOSSIP
                    NODE1 -->|内部调用| RPC
                    NODE2 -->|内部调用| RPC
                    
                    CONSOLE -->|监控数据| METRICS

                    %% ===== 样式定义 =====
                    classDef client fill:#e3f2fd,stroke:#2196f3,stroke-width:2px;
                    classDef gateway fill:#bbdefb,stroke:#1e88e5;
                    classDef service fill:#c8e6c9,stroke:#4caf50;
                    classDef storage fill:#ffecb3,stroke:#ffa000;
                    classDef cluster fill:#d1c4e9,stroke:#673ab7;
                    classDef monitor fill:#f8bbd0,stroke:#e91e63;
                    
                    class Client client;
                    class Interface_Layer gateway;
                    class Core_Services service;
                    class Persistent_Storage storage;
                    class Cluster cluster;
                    class DevOps_Observability monitor;
            </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e3f2fd; border: 2px solid #2196f3;"></div>
                <span>客户端层</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #bbdefb; border: 2px solid #1e88e5;"></div>
                <span>接入网关</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #c8e6c9; border: 2px solid #4caf50;"></div>
                <span>核心服务</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffecb3; border: 2px solid #ffa000;"></div>
                <span>数据存储</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #d1c4e9; border: 2px solid #673ab7;"></div>
                <span>服务器集群</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f8bbd0; border: 2px solid #e91e63;"></div>
                <span>监控系统</span>
            </div>
        </div>

        <div class="tech-specs">
            <h3>关键技术规格</h3>
            <ul class="tech-list">
                <li><strong>Web客户端</strong>: WebSocket + JSON 协议</li>
                <li><strong>原生客户端</strong>: KCP/UDP + Protobuf 二进制协议</li>
                <li><strong>API网关</strong>: gRPC/HTTP REST 双协议支持</li>
                <li><strong>匹配引擎</strong>: 实时计算框架，延迟 &lt; 100ms</li>
                <li><strong>数据库</strong>: PostgreSQL 主库 + CockroachDB 分布式集群</li>
                <li><strong>缓存系统</strong>: Redis 集群，支持持久化</li>
                <li><strong>集群通信</strong>: Gossip 协议 (端口 7352)</li>
                <li><strong>内部RPC</strong>: 高性能 gRPC 通信 (端口 7353)</li>
                <li><strong>监控系统</strong>: Prometheus + Grafana 监控看板</li>
                <li><strong>管理端口</strong>: 控制台 HTTPS 端口 7351</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
                nodeSpacing: 50,
                rankSpacing: 80
            },
            securityLevel: 'loose'
        });
    </script>
</body>
</html>